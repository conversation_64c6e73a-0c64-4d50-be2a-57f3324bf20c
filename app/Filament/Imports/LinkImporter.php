<?php

namespace App\Filament\Imports;

use App\Models\Link;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;
use Illuminate\Support\Number;

class LinkImporter extends Importer
{
    protected static ?string $model = Link::class;

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('id')
                ->rules(['nullable', 'integer', 'min:1'])
                ->helperText('Optional: ID of existing link to update')
                ->example(1),

            ImportColumn::make('original_url')
                ->requiredMappingForNewRecordsOnly()
                ->rules(['required', 'url', 'max:2048'])
                ->example('https://google.com/search'),

            ImportColumn::make('slug')
                ->rules(['nullable', 'string', 'max:255'])
                ->helperText('Optional custom slug for the short URL')
                ->example('google'),

            ImportColumn::make('password')
                ->rules(['nullable', 'string', 'max:255'])
                ->helperText('Optional password protection'),

            ImportColumn::make('is_active')
                ->boolean()
                ->rules(['nullable', 'boolean'])
                ->examples(['yes', 'true', 'on', 1]),

            ImportColumn::make('available_at')
                ->rules(['nullable', 'date'])
                ->helperText('When the link becomes available (optional)')
                ->example(now()),

            ImportColumn::make('unavailable_at')
                ->rules(['nullable', 'date', 'after:available_at'])
                ->helperText('When the link expires (optional)')
                ->example(now()->addWeek()),

            ImportColumn::make('forward_query_parameters')
                ->boolean()
                ->rules(['nullable', 'boolean'])
                ->examples(['yes', 'true', 'on', 1]),

            ImportColumn::make('send_ref_query_parameter')
                ->boolean()
                ->rules(['nullable', 'boolean'])
                ->examples(['yes', 'true', 'on', 1]),

            ImportColumn::make('description')
                ->rules(['nullable', 'string'])
                ->helperText('Optional description for the link'),

            ImportColumn::make('domains')
                ->relationship(resolveUsing: function (mixed$state) {

                })
                ->multiple(';')
                ->helperText('Semicolon-separated tag names (e.g., "tag1;tag2;tag3")')
                ->example('tag1;tag2;tag3'),

            ImportColumn::make('tags')
                ->relationship(resolveUsing: 'name')
                ->multiple(';')
                ->helperText('Semicolon-separated tag names (e.g., "tag1;tag2;tag3")')
                ->example('tag1;tag2;tag3'),
        ];
    }

    public function resolveRecord(): ?Link
    {
        // If ID is provided and is a valid integer, try to find existing record
        if (! empty($this->data['id']) && is_numeric($this->data['id']) && $this->data['id'] > 0) {
            $existingLink = Link::find($this->data['id']);
            if ($existingLink) {
                return $existingLink;
            }
        }

        // Otherwise create new record (without setting ID)
        return new Link;
    }

    protected function beforeFill(): void
    {
        // Remove ID from data if it's empty or null to prevent constraint violations
        if (empty($this->data['id'])) {
            unset($this->data['id']);
        }

        // Set default values for fields which are not provided
        if (! isset($this->data['is_active'])) {
            $this->data['is_active'] = true;
        }

        if (! isset($this->data['forward_query_parameters'])) {
            $this->data['forward_query_parameters'] = false;
        }

        if (! isset($this->data['send_ref_query_parameter'])) {
            $this->data['send_ref_query_parameter'] = false;
        }
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your link import has completed and '.Number::format($import->successful_rows).' '.str('row')->plural($import->successful_rows).' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' '.Number::format($failedRowsCount).' '.str('row')->plural($failedRowsCount).' failed to import.';
        }

        return $body;
    }
}
