# Link Importer

The LinkImporter allows you to bulk import links into the system using CSV files through the Filament admin panel.

## Features

- Import links from CSV files
- Support for all link attributes including tags
- Validation of required fields
- Proper handling of boolean fields
- Relationship support for tags
- Update existing links or create new ones

## CSV Format

The CSV file should include the following columns (all optional except `original_url`):

| Column | Type | Required | Description |
|--------|------|----------|-------------|
| `id` | integer | No | Existing link ID to update |
| `original_url` | string (URL) | **Yes** | The URL to shorten |
| `slug` | string | No | Custom slug for the short URL |
| `password` | string | No | Password protection |
| `is_active` | boolean | No | Whether the link is active |
| `available_at` | datetime | No | When the link becomes available |
| `unavailable_at` | datetime | No | When the link expires |
| `forward_query_parameters` | boolean | No | Forward query parameters |
| `send_ref_query_parameter` | boolean | No | Add referrer parameter |
| `description` | string | No | Link description |
| `tags` | string | No | Semicolon-separated tag names |

## Example CSV

```csv
original_url,slug,description,is_active,tags
https://example.com,example,Example website,1,social;work
https://google.com,google,Google search,1,work
https://github.com,github,GitHub repository,0,
```

## Boolean Values

For boolean columns (`is_active`, `forward_query_parameters`, `send_ref_query_parameter`), use:
- `1`, `true`, `yes` for true
- `0`, `false`, `no` for false
- Empty for default value

## Tags

Tags should be separated by semicolons (`;`). If a tag doesn't exist, it will be created automatically.

Example: `social;work;personal`

## Date Format

Dates should be in a format that PHP can parse, such as:
- `2024-01-15 10:30:00`
- `2024-01-15`
- `15/01/2024`

## Record Resolution

The importer resolves records in the following order:

1. **By ID**: If a valid `id` is provided (positive integer), it will find and update the existing link
2. **By Slug**: If a `slug` is provided (but no valid ID), it will look for a link with that short_path
3. **New Record**: If neither ID nor slug match existing records, a new link will be created

**Note**: Empty, null, or zero ID values are automatically handled and will not cause database constraint violations. The importer will treat these as new record requests.

## Validation Rules

- `original_url`: Required, must be a valid URL, max 2048 characters
- `slug`: Optional, max 255 characters
- `password`: Optional, max 255 characters
- `unavailable_at`: Must be after `available_at` if both are provided
- Boolean fields: Must be valid boolean values

## Usage in Filament

1. Navigate to the Links page in the admin panel
2. Click the "Import" button
3. Upload your CSV file
4. Map the columns to the appropriate fields
5. Review and start the import

## Error Handling

If any rows fail to import, you'll receive a notification with the number of failed rows. Common causes of import failures:

- Invalid URL format
- Missing required fields
- Validation rule violations
- Database constraint violations

**Fixed Issues**:
- ✅ Null ID constraint violations are now handled automatically
- ✅ Empty ID values are properly processed without errors
- ✅ Invalid ID values (0, negative) are treated as new record requests

## Performance

The importer is designed to handle large CSV files efficiently. For very large imports (>10,000 rows), consider breaking them into smaller batches.

## Testing

The LinkImporter includes comprehensive tests covering:
- Column configuration
- Record resolution logic
- CSV file handling
- Validation rules
- Notification generation

Run tests with:
```bash
php artisan test tests/Unit/Filament/Imports/LinkImporterTest.php
php artisan test tests/Feature/LinkImportTest.php
```
