# UserImporter Documentation

The UserImporter allows you to bulk import users into the system via CSV files through the Filament admin panel.

## CSV Format

Your CSV file should include the following columns:

| Column | Required | Type | Description |
|--------|----------|------|-------------|
| `id` | No | Integer | ID of existing user to update (leave empty for new users) |
| `name` | Yes | String | Full name of the user (max 255 characters) |
| `email` | Yes | Email | Email address - must be unique (max 255 characters) |
| `password` | Conditional | String | Password (required for new users, optional for updates, min 8 characters) |
| `is_active` | No | Boolean | Whether the user account is active (default: true) |
| `is_super_admin` | No | Boolean | Whether the user has super admin privileges (default: false) |
| `roles` | No | String | Semicolon-separated role names (e.g., "admin;editor;viewer") |

### Example CSV

```csv
id,name,email,password,is_active,is_super_admin,roles
,<PERSON>,<EMAIL>,password123,1,0,admin;editor
,<PERSON>,<EMAIL>,password456,1,1,
123,<PERSON>,<EMAIL>,,1,0,viewer
```

## Field Details

### ID Field
- **Optional**: Leave empty for new users
- **Update Mode**: Provide existing user ID to update that user
- **Validation**: Must be a positive integer if provided
- **Constraint Safe**: Empty, null, or zero values are automatically handled

### Name Field
- **Required**: Must be provided for all users
- **Type**: String with maximum length of 255 characters
- **Purpose**: Full display name of the user

### Email Field
- **Required**: Must be provided for all users
- **Type**: Valid email address with maximum length of 255 characters
- **Unique**: Email addresses must be unique across all users
- **Update Resolution**: If no ID is provided, existing users are found by email

### Password Field
- **Conditional**: Required for new users, optional for existing user updates
- **Type**: String with minimum length of 8 characters
- **Security**: Automatically hashed using Laravel's Hash facade
- **Updates**: Leave empty to keep existing password when updating users

### Boolean Fields (is_active, is_super_admin)
- **Format**: Use `1` for true, `0` for false, or leave empty for defaults
- **Defaults**: 
  - `is_active`: true (users are active by default)
  - `is_super_admin`: false (users are not super admins by default)

### Roles Field
- **Format**: Semicolon-separated role names (e.g., "admin;editor;viewer")
- **Optional**: Leave empty if no roles should be assigned
- **Validation**: Role names must exist in the system
- **Assignment**: Roles are assigned to the user after import

## Record Resolution

The importer resolves records in the following order:

1. **By ID**: If a valid `id` is provided (positive integer), it will find and update the existing user
2. **By Email**: If an `email` is provided (but no valid ID), it will look for a user with that email address
3. **New Record**: If neither ID nor email match existing records, a new user will be created

**Note**: Empty, null, or zero ID values are automatically handled and will not cause database constraint violations. The importer will treat these as new record requests.

## Usage Instructions

1. **Prepare your CSV file** with the required columns and data
2. **Navigate to Users** in the Filament admin panel
3. **Click the Import button** in the toolbar
4. **Upload your CSV file** and configure the column mappings
5. **Review the preview** and start the import process
6. **Monitor progress** and review any error messages

## Validation Rules

- **Name**: Required, string, maximum 255 characters
- **Email**: Required, valid email format, unique, maximum 255 characters
- **Password**: Required for new users, minimum 8 characters for new passwords
- **ID**: Optional, positive integer if provided
- **Boolean fields**: Must be boolean values (0, 1, true, false) if provided
- **Roles**: Must reference existing role names in the system

## Error Handling

If any rows fail to import, you'll receive a notification with the number of failed rows. Common causes of import failures:

- Invalid email format or duplicate email addresses
- Missing required fields (name, email, password for new users)
- Invalid role names that don't exist in the system
- Validation rule violations
- Database constraint violations

**Fixed Issues**:
- ✅ Null ID constraint violations are now handled automatically
- ✅ Empty ID values are properly processed without errors
- ✅ Invalid ID values (0, negative) are treated as new record requests
- ✅ Password hashing is handled automatically
- ✅ Default boolean values are set appropriately

## Security Considerations

- **Passwords are automatically hashed** using Laravel's secure hashing
- **Email uniqueness is enforced** to prevent duplicate accounts
- **Role assignments are validated** against existing roles
- **Super admin privileges** should be assigned carefully
- **User activation status** can be controlled via the `is_active` field

## Performance Notes

- Large imports are processed in batches for better performance
- Role assignments are handled efficiently using Spatie Permission package
- Password hashing may slow down very large imports
- Consider importing users in smaller batches (< 1000 rows) for optimal performance

## Troubleshooting

### Common Issues

1. **"Password is required for new users"**
   - Ensure new users (without ID or with non-existent email) have passwords

2. **"Email already exists"**
   - Check for duplicate emails in your CSV or existing database

3. **"Role not found"**
   - Verify all role names in the roles column exist in the system

4. **"Invalid email format"**
   - Ensure all email addresses are properly formatted

### Best Practices

- **Test with a small sample** before importing large datasets
- **Backup your database** before large imports
- **Validate your CSV** format and data before uploading
- **Create required roles** before importing users that reference them
- **Use consistent boolean formats** (0/1 or true/false)
