<?php

namespace Tests\Feature;

use App\Filament\Imports\LinkImporter;
use App\Models\Domain;
use App\Models\Link;
use App\Models\User;
use Filament\Actions\Imports\Models\Import;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class LinkImportConstraintTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an active domain with admin panel to satisfy validation
        Domain::factory()->create([
            'host' => 'default-test-domain.com',
            'is_active' => true,
            'is_admin_panel_active' => true,
        ]);

        // Create a test user for imports
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);
    }

    #[Test]
    public function it_handles_csv_with_empty_id_column_without_constraint_violation(): void
    {
        // Create CSV content with empty ID values (common scenario)
        $csvContent = "id,original_url,slug,description,is_active\n";
        $csvContent .= ",https://example.com,example,Example website,1\n";
        $csvContent .= ",https://google.com,google,Google search,1\n";
        $csvContent .= ",https://github.com,github,GitHub repository,0\n";

        // Create a temporary file
        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('links_with_empty_ids.csv', $csvContent);
        $filePath = $file->store('imports', 'local');

        // Create import record
        $import = Import::create([
            'file_name' => 'links_with_empty_ids.csv',
            'file_path' => $filePath,
            'importer' => LinkImporter::class,
            'total_rows' => 3,
            'user_id' => $this->user->id,
        ]);

        // Test that we can create the importer without issues
        $importer = new LinkImporter($import, [], []);
        $this->assertInstanceOf(LinkImporter::class, $importer);

        // Simulate processing rows with empty IDs
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        // Test each row from the CSV
        $testRows = [
            ['id' => '', 'original_url' => 'https://example.com', 'slug' => 'example'],
            ['id' => '', 'original_url' => 'https://google.com', 'slug' => 'google'],
            ['id' => '', 'original_url' => 'https://github.com', 'slug' => 'github'],
        ];

        foreach ($testRows as $rowData) {
            $dataProperty->setValue($importer, $rowData);

            // This should not throw a constraint violation
            $record = $importer->resolveRecord();
            $this->assertInstanceOf(Link::class, $record);
            $this->assertFalse($record->exists); // Should be a new record
            $this->assertNull($record->id); // ID should be null for new records
        }
    }

    #[Test]
    public function it_handles_csv_with_null_id_column_without_constraint_violation(): void
    {
        // Create import record
        $import = Import::create([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => LinkImporter::class,
            'total_rows' => 1,
            'user_id' => $this->user->id,
        ]);

        $importer = new LinkImporter($import, [], []);

        // Use reflection to test the beforeFill method
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        $beforeFillMethod = $reflection->getMethod('beforeFill');
        $beforeFillMethod->setAccessible(true);

        // Test with empty string ID
        $dataProperty->setValue($importer, ['id' => '', 'original_url' => 'https://example.com']);
        $beforeFillMethod->invoke($importer);
        $data = $dataProperty->getValue($importer);
        $this->assertArrayNotHasKey('id', $data); // ID should be removed

        // Test with null ID
        $dataProperty->setValue($importer, ['id' => null, 'original_url' => 'https://example.com']);
        $beforeFillMethod->invoke($importer);
        $data = $dataProperty->getValue($importer);
        $this->assertArrayNotHasKey('id', $data); // ID should be removed

        // Test with valid ID (should be kept)
        $dataProperty->setValue($importer, ['id' => 123, 'original_url' => 'https://example.com']);
        $beforeFillMethod->invoke($importer);
        $data = $dataProperty->getValue($importer);
        $this->assertArrayHasKey('id', $data); // ID should be kept
        $this->assertEquals(123, $data['id']);
    }

    #[Test]
    public function it_can_update_existing_links_by_valid_id(): void
    {
        // Create an existing link
        $existingLink = Link::factory()->create([
            'original_url' => 'https://old-url.com',
            'slug' => 'old-slug',
        ]);

        // Create import record
        $import = Import::create([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => LinkImporter::class,
            'total_rows' => 1,
            'user_id' => $this->user->id,
        ]);

        $importer = new LinkImporter($import, [], []);

        // Use reflection to test updating existing record
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        // Test with existing ID
        $dataProperty->setValue($importer, [
            'id' => $existingLink->id,
            'original_url' => 'https://updated-url.com',
            'slug' => 'updated-slug',
        ]);

        $record = $importer->resolveRecord();
        $this->assertEquals($existingLink->id, $record->id);
        $this->assertTrue($record->exists);
        $this->assertEquals('https://old-url.com', $record->original_url); // Should be the existing record
    }

    #[Test]
    public function it_creates_new_links_when_id_not_found(): void
    {
        // Create import record
        $import = Import::create([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => LinkImporter::class,
            'total_rows' => 1,
            'user_id' => $this->user->id,
        ]);

        $importer = new LinkImporter($import, [], []);

        // Use reflection to test with non-existent ID
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        // Test with non-existent ID
        $dataProperty->setValue($importer, [
            'id' => 99999, // Non-existent ID
            'original_url' => 'https://new-url.com',
            'slug' => 'new-slug',
        ]);

        $record = $importer->resolveRecord();
        $this->assertInstanceOf(Link::class, $record);
        $this->assertFalse($record->exists); // Should be a new record
        $this->assertNull($record->id); // ID should be null for new records
    }
}
