<?php

namespace Tests\Feature;

use App\Filament\Imports\LinkImporter;
use App\Models\Domain;
use App\Models\Link;
use App\Models\Tag;
use App\Models\User;
use Filament\Actions\Imports\Models\Import;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class LinkImportTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an active domain with admin panel to satisfy validation
        Domain::factory()->create([
            'host' => 'default-test-domain.com',
            'is_active' => true,
            'is_admin_panel_active' => true,
        ]);

        // Create a test user for imports
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);
    }

    #[Test]
    public function it_can_import_links_from_csv(): void
    {
        // Create some tags first
        $tag1 = Tag::factory()->create(['name' => 'social']);
        $tag2 = Tag::factory()->create(['name' => 'work']);

        // Create CSV content
        $csvContent = "original_url,slug,description,is_active,tags\n";
        $csvContent .= "https://example.com,example,Example website,1,social;work\n";
        $csvContent .= "https://google.com,google,Google search,1,work\n";
        $csvContent .= "https://github.com,github,GitHub repository,0,\n";

        // Create a temporary file
        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('links.csv', $csvContent);
        $filePath = $file->store('imports', 'local');

        // Create import record
        $import = Import::create([
            'file_name' => 'links.csv',
            'file_path' => $filePath,
            'importer' => LinkImporter::class,
            'total_rows' => 3,
            'user_id' => $this->user->id,
        ]);

        // Test that we can create the importer
        $importer = new LinkImporter($import, [], []);
        $this->assertInstanceOf(LinkImporter::class, $importer);

        // Verify the CSV file was created
        Storage::disk('local')->assertExists($filePath);

        // Verify the content
        $content = Storage::disk('local')->get($filePath);
        $this->assertStringContainsString('https://example.com', $content);
        $this->assertStringContainsString('https://google.com', $content);
        $this->assertStringContainsString('https://github.com', $content);
    }

    #[Test]
    public function it_has_correct_column_configuration(): void
    {
        $columns = LinkImporter::getColumns();

        // Verify we have all expected columns
        $columnNames = array_map(fn ($column) => $column->getName(), $columns);

        $expectedColumns = [
            'id',
            'original_url',
            'slug',
            'password',
            'is_active',
            'available_at',
            'unavailable_at',
            'forward_query_parameters',
            'send_ref_query_parameter',
            'description',
            'tags',
        ];

        foreach ($expectedColumns as $expectedColumn) {
            $this->assertContains($expectedColumn, $columnNames);
        }

        // Verify boolean columns are properly configured
        $booleanColumns = ['is_active', 'forward_query_parameters', 'send_ref_query_parameter'];
        foreach ($booleanColumns as $columnName) {
            $column = collect($columns)->first(fn ($column) => $column->getName() === $columnName);
            $this->assertTrue($column->isBoolean());
        }
    }

    #[Test]
    public function it_can_resolve_new_records(): void
    {
        // Create import record
        $import = Import::create([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => LinkImporter::class,
            'total_rows' => 1,
            'user_id' => $this->user->id,
        ]);

        $importer = new LinkImporter($import, [], []);

        // Use reflection to test the resolveRecord method with different data
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        // Test with no identifying data - should create new record
        $dataProperty->setValue($importer, ['original_url' => 'https://example.com']);
        $record = $importer->resolveRecord();
        $this->assertInstanceOf(Link::class, $record);
        $this->assertFalse($record->exists);
    }

    #[Test]
    public function it_can_resolve_existing_records_by_id(): void
    {
        $existingLink = Link::factory()->create();

        // Create import record
        $import = Import::create([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => LinkImporter::class,
            'total_rows' => 1,
            'user_id' => $this->user->id,
        ]);

        $importer = new LinkImporter($import, [], []);

        // Use reflection to test the resolveRecord method
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        // Test with existing ID
        $dataProperty->setValue($importer, ['id' => $existingLink->id]);
        $record = $importer->resolveRecord();
        $this->assertEquals($existingLink->id, $record->id);
        $this->assertTrue($record->exists);
    }

    #[Test]
    public function it_generates_proper_completion_notification(): void
    {
        // Create import record with some stats
        $import = Import::create([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => LinkImporter::class,
            'total_rows' => 10,
            'successful_rows' => 8,
            'processed_rows' => 10,
            'user_id' => $this->user->id,
        ]);

        $notification = LinkImporter::getCompletedNotificationBody($import);

        $this->assertStringContainsString('8', $notification);
        $this->assertStringContainsString('rows imported', $notification);
        $this->assertStringContainsString('2', $notification); // Failed rows
        $this->assertStringContainsString('failed to import', $notification);
    }

    #[Test]
    public function it_handles_empty_id_values_without_constraint_violation(): void
    {
        // Create import record
        $import = Import::create([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => LinkImporter::class,
            'total_rows' => 1,
            'user_id' => $this->user->id,
        ]);

        $importer = new LinkImporter($import, [], []);

        // Use reflection to test with empty/null ID values
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        // Test with empty string ID
        $dataProperty->setValue($importer, ['id' => '', 'original_url' => 'https://example.com']);
        $record = $importer->resolveRecord();
        $this->assertInstanceOf(Link::class, $record);
        $this->assertFalse($record->exists);

        // Test with null ID
        $dataProperty->setValue($importer, ['id' => null, 'original_url' => 'https://example.com']);
        $record = $importer->resolveRecord();
        $this->assertInstanceOf(Link::class, $record);
        $this->assertFalse($record->exists);

        // Test with zero ID
        $dataProperty->setValue($importer, ['id' => 0, 'original_url' => 'https://example.com']);
        $record = $importer->resolveRecord();
        $this->assertInstanceOf(Link::class, $record);
        $this->assertFalse($record->exists);
    }
}
