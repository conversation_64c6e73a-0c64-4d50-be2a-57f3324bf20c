<?php

namespace Tests\Feature;

use App\Filament\Imports\UserImporter;
use App\Models\Role;
use App\Models\User;
use Filament\Actions\Imports\Models\Import;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class UserImportConstraintTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test user for imports
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        // Create some roles for testing
        Role::create(['name' => 'admin']);
        Role::create(['name' => 'editor']);
    }

    #[Test]
    public function it_handles_csv_with_empty_id_column_without_constraint_violation(): void
    {
        // Create CSV content with empty ID values (common scenario)
        $csvContent = "id,name,email,password,is_active,is_super_admin\n";
        $csvContent .= ",<PERSON>,<EMAIL>,password123,1,0\n";
        $csvContent .= ",<PERSON>,<EMAIL>,password456,1,1\n";
        $csvContent .= ",Bob Wilson,<EMAIL>,password789,0,0\n";

        // Create a temporary file
        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('users_with_empty_ids.csv', $csvContent);
        $filePath = $file->store('imports', 'local');

        // Create import record
        $import = Import::create([
            'file_name' => 'users_with_empty_ids.csv',
            'file_path' => $filePath,
            'importer' => UserImporter::class,
            'total_rows' => 3,
            'user_id' => $this->user->id,
        ]);

        // Test that we can create the importer without issues
        $importer = new UserImporter($import, [], []);
        $this->assertInstanceOf(UserImporter::class, $importer);

        // Simulate processing rows with empty IDs
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        // Test each row from the CSV
        $testRows = [
            ['id' => '', 'name' => 'John Doe', 'email' => '<EMAIL>', 'password' => 'password123'],
            ['id' => '', 'name' => 'Jane Smith', 'email' => '<EMAIL>', 'password' => 'password456'],
            ['id' => '', 'name' => 'Bob Wilson', 'email' => '<EMAIL>', 'password' => 'password789'],
        ];

        foreach ($testRows as $rowData) {
            $dataProperty->setValue($importer, $rowData);

            // This should not throw a constraint violation
            $record = $importer->resolveRecord();
            $this->assertInstanceOf(User::class, $record);
            $this->assertFalse($record->exists); // Should be a new record
            $this->assertNull($record->id); // ID should be null for new records
        }
    }

    #[Test]
    public function it_handles_csv_with_null_id_column_without_constraint_violation(): void
    {
        // Create import record
        $import = Import::create([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => UserImporter::class,
            'total_rows' => 1,
            'user_id' => $this->user->id,
        ]);

        $importer = new UserImporter($import, [], []);

        // Use reflection to test the beforeFill method
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        $beforeFillMethod = $reflection->getMethod('beforeFill');
        $beforeFillMethod->setAccessible(true);

        // Test with empty string ID
        $dataProperty->setValue($importer, ['id' => '', 'name' => 'Test', 'email' => '<EMAIL>']);
        $beforeFillMethod->invoke($importer);
        $data = $dataProperty->getValue($importer);
        $this->assertArrayNotHasKey('id', $data); // ID should be removed

        // Test with null ID
        $dataProperty->setValue($importer, ['id' => null, 'name' => 'Test', 'email' => '<EMAIL>']);
        $beforeFillMethod->invoke($importer);
        $data = $dataProperty->getValue($importer);
        $this->assertArrayNotHasKey('id', $data); // ID should be removed

        // Test with valid ID (should be kept)
        $dataProperty->setValue($importer, ['id' => 123, 'name' => 'Test', 'email' => '<EMAIL>']);
        $beforeFillMethod->invoke($importer);
        $data = $dataProperty->getValue($importer);
        $this->assertArrayHasKey('id', $data); // ID should be kept
        $this->assertEquals(123, $data['id']);
    }

    #[Test]
    public function it_can_update_existing_users_by_valid_id(): void
    {
        // Create an existing user
        $existingUser = User::factory()->create([
            'name' => 'Old Name',
            'email' => '<EMAIL>',
        ]);

        // Create import record
        $import = Import::create([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => UserImporter::class,
            'total_rows' => 1,
            'user_id' => $this->user->id,
        ]);

        $importer = new UserImporter($import, [], []);

        // Use reflection to test updating existing record
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        // Test with existing ID
        $dataProperty->setValue($importer, [
            'id' => $existingUser->id,
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
        ]);

        $record = $importer->resolveRecord();
        $this->assertEquals($existingUser->id, $record->id);
        $this->assertTrue($record->exists);
        $this->assertEquals('Old Name', $record->name); // Should be the existing record
    }

    #[Test]
    public function it_can_update_existing_users_by_email(): void
    {
        // Create an existing user
        $existingUser = User::factory()->create([
            'name' => 'Existing User',
            'email' => '<EMAIL>',
        ]);

        // Create import record
        $import = Import::create([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => UserImporter::class,
            'total_rows' => 1,
            'user_id' => $this->user->id,
        ]);

        $importer = new UserImporter($import, [], []);

        // Use reflection to test updating by email
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        // Test with existing email (no ID provided)
        $dataProperty->setValue($importer, [
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
        ]);

        $record = $importer->resolveRecord();
        $this->assertEquals($existingUser->id, $record->id);
        $this->assertTrue($record->exists);
        $this->assertEquals('Existing User', $record->name); // Should be the existing record
    }

    #[Test]
    public function it_creates_new_users_when_id_not_found(): void
    {
        // Create import record
        $import = Import::create([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => UserImporter::class,
            'total_rows' => 1,
            'user_id' => $this->user->id,
        ]);

        $importer = new UserImporter($import, [], []);

        // Use reflection to test with non-existent ID
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        // Test with non-existent ID
        $dataProperty->setValue($importer, [
            'id' => 99999, // Non-existent ID
            'name' => 'New User',
            'email' => '<EMAIL>',
        ]);

        $record = $importer->resolveRecord();
        $this->assertInstanceOf(User::class, $record);
        $this->assertFalse($record->exists); // Should be a new record
        $this->assertNull($record->id); // ID should be null for new records
    }

    #[Test]
    public function it_creates_new_users_when_email_not_found(): void
    {
        // Create import record
        $import = Import::create([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => UserImporter::class,
            'total_rows' => 1,
            'user_id' => $this->user->id,
        ]);

        $importer = new UserImporter($import, [], []);

        // Use reflection to test with non-existent email
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        // Test with non-existent email
        $dataProperty->setValue($importer, [
            'name' => 'New User',
            'email' => '<EMAIL>',
        ]);

        $record = $importer->resolveRecord();
        $this->assertInstanceOf(User::class, $record);
        $this->assertFalse($record->exists); // Should be a new record
        $this->assertNull($record->id); // ID should be null for new records
    }

    #[Test]
    public function it_validates_password_requirement_for_new_users(): void
    {
        // Create import record
        $import = Import::create([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => UserImporter::class,
            'total_rows' => 1,
            'user_id' => $this->user->id,
        ]);

        $importer = new UserImporter($import, [], []);

        // Use reflection to test password validation
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        $beforeValidateMethod = $reflection->getMethod('beforeValidate');
        $beforeValidateMethod->setAccessible(true);

        // Test new user without password - should throw exception
        $dataProperty->setValue($importer, [
            'name' => 'New User',
            'email' => '<EMAIL>',
            // No password provided
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Password is required for new users.');
        $beforeValidateMethod->invoke($importer);
    }
}
