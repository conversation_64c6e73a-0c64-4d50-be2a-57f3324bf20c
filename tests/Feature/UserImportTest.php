<?php

namespace Tests\Feature;

use App\Filament\Imports\UserImporter;
use App\Models\Role;
use App\Models\User;
use Filament\Actions\Imports\Models\Import;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class UserImportTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test user for imports
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        // Create some roles for testing
        Role::create(['name' => 'admin']);
        Role::create(['name' => 'editor']);
        Role::create(['name' => 'viewer']);
    }

    #[Test]
    public function it_can_import_users_from_csv(): void
    {
        // Create CSV content
        $csvContent = "name,email,password,is_active,is_super_admin,roles\n";
        $csvContent .= "<PERSON>,<EMAIL>,password123,1,0,admin;editor\n";
        $csvContent .= "Jane Smith,<EMAIL>,password456,1,1,\n";
        $csvContent .= "Bob Wilson,<EMAIL>,password789,0,0,viewer\n";

        // Create a temporary file
        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('users.csv', $csvContent);
        $filePath = $file->store('imports', 'local');

        // Create import record
        $import = Import::create([
            'file_name' => 'users.csv',
            'file_path' => $filePath,
            'importer' => UserImporter::class,
            'total_rows' => 3,
            'user_id' => $this->user->id,
        ]);

        // Test that we can create the importer
        $importer = new UserImporter($import, [], []);
        $this->assertInstanceOf(UserImporter::class, $importer);

        // Verify the CSV file was created
        Storage::disk('local')->assertExists($filePath);

        // Verify the content
        $content = Storage::disk('local')->get($filePath);
        $this->assertStringContainsString('<EMAIL>', $content);
        $this->assertStringContainsString('<EMAIL>', $content);
        $this->assertStringContainsString('<EMAIL>', $content);
    }

    #[Test]
    public function it_can_resolve_new_records(): void
    {
        // Create import record
        $import = Import::create([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => UserImporter::class,
            'total_rows' => 1,
            'user_id' => $this->user->id,
        ]);

        $importer = new UserImporter($import, [], []);

        // Use reflection to test the resolveRecord method with different data
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        // Test with no identifying data - should create new record
        $dataProperty->setValue($importer, [
            'name' => 'New User',
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);
        $record = $importer->resolveRecord();
        $this->assertInstanceOf(User::class, $record);
        $this->assertFalse($record->exists);
    }

    #[Test]
    public function it_can_resolve_existing_records_by_id(): void
    {
        $existingUser = User::factory()->create([
            'name' => 'Existing User',
            'email' => '<EMAIL>',
        ]);

        // Create import record
        $import = Import::create([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => UserImporter::class,
            'total_rows' => 1,
            'user_id' => $this->user->id,
        ]);

        $importer = new UserImporter($import, [], []);

        // Use reflection to test the resolveRecord method
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        // Test with existing ID
        $dataProperty->setValue($importer, ['id' => $existingUser->id]);
        $record = $importer->resolveRecord();
        $this->assertEquals($existingUser->id, $record->id);
        $this->assertTrue($record->exists);
    }

    #[Test]
    public function it_can_resolve_existing_records_by_email(): void
    {
        $existingUser = User::factory()->create([
            'name' => 'Existing User',
            'email' => '<EMAIL>',
        ]);

        // Create import record
        $import = Import::create([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => UserImporter::class,
            'total_rows' => 1,
            'user_id' => $this->user->id,
        ]);

        $importer = new UserImporter($import, [], []);

        // Use reflection to test the resolveRecord method
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        // Test with existing email
        $dataProperty->setValue($importer, ['email' => '<EMAIL>']);
        $record = $importer->resolveRecord();
        $this->assertEquals($existingUser->id, $record->id);
        $this->assertTrue($record->exists);
    }

    #[Test]
    public function it_handles_password_hashing(): void
    {
        // Create import record
        $import = Import::create([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => UserImporter::class,
            'total_rows' => 1,
            'user_id' => $this->user->id,
        ]);

        $importer = new UserImporter($import, [], []);

        // Use reflection to test the beforeFill method
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        $beforeFillMethod = $reflection->getMethod('beforeFill');
        $beforeFillMethod->setAccessible(true);

        // Test password hashing
        $plainPassword = 'password123';
        $dataProperty->setValue($importer, [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => $plainPassword,
        ]);

        $beforeFillMethod->invoke($importer);
        $data = $dataProperty->getValue($importer);

        $this->assertNotEquals($plainPassword, $data['password']);
        $this->assertTrue(Hash::check($plainPassword, $data['password']));
    }

    #[Test]
    public function it_sets_default_boolean_values(): void
    {
        // Create import record
        $import = Import::create([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => UserImporter::class,
            'total_rows' => 1,
            'user_id' => $this->user->id,
        ]);

        $importer = new UserImporter($import, [], []);

        // Use reflection to test the beforeFill method
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        $beforeFillMethod = $reflection->getMethod('beforeFill');
        $beforeFillMethod->setAccessible(true);

        // Test with no boolean values provided
        $dataProperty->setValue($importer, [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $beforeFillMethod->invoke($importer);
        $data = $dataProperty->getValue($importer);

        $this->assertTrue($data['is_active']);
        $this->assertFalse($data['is_super_admin']);
    }

    #[Test]
    public function it_handles_empty_id_values_without_constraint_violation(): void
    {
        // Create import record
        $import = Import::create([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => UserImporter::class,
            'total_rows' => 1,
            'user_id' => $this->user->id,
        ]);

        $importer = new UserImporter($import, [], []);

        // Use reflection to test the beforeFill method
        $reflection = new \ReflectionClass($importer);
        $dataProperty = $reflection->getProperty('data');
        $dataProperty->setAccessible(true);

        $beforeFillMethod = $reflection->getMethod('beforeFill');
        $beforeFillMethod->setAccessible(true);

        // Test with empty string ID
        $dataProperty->setValue($importer, ['id' => '', 'name' => 'Test', 'email' => '<EMAIL>']);
        $beforeFillMethod->invoke($importer);
        $data = $dataProperty->getValue($importer);
        $this->assertArrayNotHasKey('id', $data);

        // Test with null ID
        $dataProperty->setValue($importer, ['id' => null, 'name' => 'Test', 'email' => '<EMAIL>']);
        $beforeFillMethod->invoke($importer);
        $data = $dataProperty->getValue($importer);
        $this->assertArrayNotHasKey('id', $data);

        // Test with valid ID (should be kept)
        $dataProperty->setValue($importer, ['id' => 123, 'name' => 'Test', 'email' => '<EMAIL>']);
        $beforeFillMethod->invoke($importer);
        $data = $dataProperty->getValue($importer);
        $this->assertArrayHasKey('id', $data);
        $this->assertEquals(123, $data['id']);
    }
}
