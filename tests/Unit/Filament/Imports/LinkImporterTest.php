<?php

namespace Tests\Unit\Filament\Imports;

use App\Filament\Imports\LinkImporter;
use App\Models\Domain;
use App\Models\Link;
use App\Models\User;
use Filament\Actions\Imports\ImportColumn;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class LinkImporterTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an active domain with admin panel to satisfy validation
        Domain::factory()->create([
            'host' => 'default-test-domain.com',
            'is_active' => true,
            'is_admin_panel_active' => true,
        ]);

        // Create a test user for imports
        User::factory()->create([
            'id' => 1,
            'email' => '<EMAIL>',
        ]);
    }

    #[Test]
    public function it_has_correct_model(): void
    {
        $this->assertEquals(Link::class, LinkImporter::getModel());
    }

    #[Test]
    public function it_has_required_columns(): void
    {
        $columns = LinkImporter::getColumns();
        $columnNames = array_map(fn ($column) => $column->getName(), $columns);

        $expectedColumns = [
            'id',
            'original_url',
            'slug',
            'password',
            'is_active',
            'available_at',
            'unavailable_at',
            'forward_query_parameters',
            'send_ref_query_parameter',
            'description',
            'tags',
        ];

        foreach ($expectedColumns as $expectedColumn) {
            $this->assertContains($expectedColumn, $columnNames);
        }
    }

    #[Test]
    public function it_validates_original_url_as_required(): void
    {
        $columns = LinkImporter::getColumns();
        $originalUrlColumn = collect($columns)->first(fn ($column) => $column->getName() === 'original_url');

        $this->assertInstanceOf(ImportColumn::class, $originalUrlColumn);
        // Test that the column exists and is properly configured
        $this->assertEquals('original_url', $originalUrlColumn->getName());
    }

    #[Test]
    public function it_can_instantiate_importer(): void
    {
        // Create a mock import and importer instance
        $import = new \Filament\Actions\Imports\Models\Import([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => LinkImporter::class,
            'total_rows' => 1,
            'user_id' => 1,
        ]);
        $import->save();

        $importer = new LinkImporter($import, [], []);

        $this->assertInstanceOf(LinkImporter::class, $importer);
    }

    #[Test]
    public function it_handles_tags_relationship(): void
    {
        $columns = LinkImporter::getColumns();
        $tagsColumn = collect($columns)->first(fn ($column) => $column->getName() === 'tags');

        $this->assertInstanceOf(ImportColumn::class, $tagsColumn);
        // Test that the tags column exists
        $this->assertEquals('tags', $tagsColumn->getName());
    }

    #[Test]
    public function it_has_proper_boolean_columns(): void
    {
        $columns = LinkImporter::getColumns();
        $booleanColumns = ['is_active', 'forward_query_parameters', 'send_ref_query_parameter'];

        foreach ($booleanColumns as $columnName) {
            $column = collect($columns)->first(fn ($column) => $column->getName() === $columnName);
            $this->assertInstanceOf(ImportColumn::class, $column);
            $this->assertTrue($column->isBoolean());
        }
    }

    #[Test]
    public function it_has_proper_validation_rules(): void
    {
        $columns = LinkImporter::getColumns();

        // Test that key columns exist
        $originalUrlColumn = collect($columns)->first(fn ($column) => $column->getName() === 'original_url');
        $this->assertInstanceOf(ImportColumn::class, $originalUrlColumn);

        $unavailableAtColumn = collect($columns)->first(fn ($column) => $column->getName() === 'unavailable_at');
        $this->assertInstanceOf(ImportColumn::class, $unavailableAtColumn);

        // Test that all expected columns are present
        $this->assertCount(11, $columns);
    }
}
