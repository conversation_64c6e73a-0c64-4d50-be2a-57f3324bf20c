<?php

namespace Tests\Unit\Filament\Imports;

use App\Filament\Imports\UserImporter;
use App\Models\User;
use Filament\Actions\Imports\ImportColumn;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class UserImporterTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test user for imports
        User::factory()->create([
            'id' => 1,
            'email' => '<EMAIL>',
        ]);
    }

    #[Test]
    public function it_has_correct_model(): void
    {
        $this->assertEquals(User::class, UserImporter::getModel());
    }

    #[Test]
    public function it_has_required_columns(): void
    {
        $columns = UserImporter::getColumns();
        $columnNames = array_map(fn ($column) => $column->getName(), $columns);

        $expectedColumns = [
            'id',
            'name',
            'email',
            'password',
            'is_active',
            'is_super_admin',
            'roles',
        ];

        foreach ($expectedColumns as $expectedColumn) {
            $this->assertContains($expectedColumn, $columnNames);
        }
    }

    #[Test]
    public function it_validates_required_fields(): void
    {
        $columns = UserImporter::getColumns();

        // Name should be required
        $nameColumn = collect($columns)->first(fn ($column) => $column->getName() === 'name');
        $this->assertInstanceOf(ImportColumn::class, $nameColumn);
        $this->assertEquals('name', $nameColumn->getName());

        // Email should be required
        $emailColumn = collect($columns)->first(fn ($column) => $column->getName() === 'email');
        $this->assertInstanceOf(ImportColumn::class, $emailColumn);
        $this->assertEquals('email', $emailColumn->getName());
    }

    #[Test]
    public function it_can_instantiate_importer(): void
    {
        // Create a mock import and importer instance
        $import = new \Filament\Actions\Imports\Models\Import([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => UserImporter::class,
            'total_rows' => 1,
            'user_id' => 1,
        ]);
        $import->save();

        $importer = new UserImporter($import, [], []);

        $this->assertInstanceOf(UserImporter::class, $importer);
    }

    #[Test]
    public function it_handles_roles_relationship(): void
    {
        $columns = UserImporter::getColumns();
        $rolesColumn = collect($columns)->first(fn ($column) => $column->getName() === 'roles');

        $this->assertInstanceOf(ImportColumn::class, $rolesColumn);
        // Test that the roles column exists
        $this->assertEquals('roles', $rolesColumn->getName());
    }

    #[Test]
    public function it_has_proper_boolean_columns(): void
    {
        $columns = UserImporter::getColumns();
        $booleanColumns = ['is_active', 'is_super_admin'];

        foreach ($booleanColumns as $columnName) {
            $column = collect($columns)->first(fn ($column) => $column->getName() === $columnName);
            $this->assertInstanceOf(ImportColumn::class, $column);
            $this->assertTrue($column->isBoolean());
        }
    }

    #[Test]
    public function it_has_proper_validation_rules(): void
    {
        $columns = UserImporter::getColumns();

        // Test that key columns exist
        $nameColumn = collect($columns)->first(fn ($column) => $column->getName() === 'name');
        $this->assertInstanceOf(ImportColumn::class, $nameColumn);

        $emailColumn = collect($columns)->first(fn ($column) => $column->getName() === 'email');
        $this->assertInstanceOf(ImportColumn::class, $emailColumn);

        $passwordColumn = collect($columns)->first(fn ($column) => $column->getName() === 'password');
        $this->assertInstanceOf(ImportColumn::class, $passwordColumn);

        // Test that all expected columns are present
        $this->assertCount(7, $columns);
    }

    #[Test]
    public function it_generates_proper_completion_notification(): void
    {
        // Create import record with some stats
        $import = new \Filament\Actions\Imports\Models\Import([
            'file_name' => 'test.csv',
            'file_path' => 'test.csv',
            'importer' => UserImporter::class,
            'total_rows' => 10,
            'successful_rows' => 8,
            'processed_rows' => 10,
            'user_id' => 1,
        ]);
        $import->save();

        $notification = UserImporter::getCompletedNotificationBody($import);

        $this->assertStringContainsString('8', $notification);
        $this->assertStringContainsString('rows imported', $notification);
        $this->assertStringContainsString('2', $notification); // Failed rows
        $this->assertStringContainsString('failed to import', $notification);
    }
}
